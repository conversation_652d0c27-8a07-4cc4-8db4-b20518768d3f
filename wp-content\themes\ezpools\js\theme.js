/**
 * EZ Pools Theme JavaScript
 */

(function($) {
    'use strict';

    $(document).ready(function() {
        
        // Mobile menu toggle
        $('.mobile-menu-toggle').on('click', function() {
            $('.main-nav').toggleClass('active');
            $(this).toggleClass('active');
        });

        // Smooth scrolling for anchor links
        $('a[href^="#"]').on('click', function(e) {
            e.preventDefault();
            
            var target = $(this.getAttribute('href'));
            if (target.length) {
                $('html, body').stop().animate({
                    scrollTop: target.offset().top - 80
                }, 1000);
            }
        });

        // Header scroll effect
        $(window).on('scroll', function() {
            var scroll = $(window).scrollTop();
            
            if (scroll >= 100) {
                $('.site-header').addClass('scrolled');
            } else {
                $('.site-header').removeClass('scrolled');
            }
        });

        // Animate elements on scroll
        function animateOnScroll() {
            $('.feature-card, .product-card').each(function() {
                var elementTop = $(this).offset().top;
                var elementBottom = elementTop + $(this).outerHeight();
                var viewportTop = $(window).scrollTop();
                var viewportBottom = viewportTop + $(window).height();

                if (elementBottom > viewportTop && elementTop < viewportBottom) {
                    $(this).addClass('animate-in');
                }
            });
        }

        // Run animation on scroll
        $(window).on('scroll', animateOnScroll);
        animateOnScroll(); // Run on page load

        // Contact form handling
        $('.contact-form').on('submit', function(e) {
            e.preventDefault();
            
            var form = $(this);
            var formData = form.serialize();
            
            // Add loading state
            form.find('.submit-btn').prop('disabled', true).text('Sending...');
            
            // Simulate form submission (replace with actual AJAX call)
            setTimeout(function() {
                form.find('.submit-btn').prop('disabled', false).text('Send Message');
                $('.form-success').show();
                form[0].reset();
            }, 2000);
        });

        // Product filter functionality
        $('.product-filter').on('click', function(e) {
            e.preventDefault();
            
            var filter = $(this).data('filter');
            
            // Update active filter
            $('.product-filter').removeClass('active');
            $(this).addClass('active');
            
            // Filter products
            if (filter === 'all') {
                $('.product-card').show();
            } else {
                $('.product-card').hide();
                $('.product-card[data-category="' + filter + '"]').show();
            }
        });

        // Image lazy loading
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }

        // Testimonial slider
        let currentTestimonial = 0;
        const testimonials = $('.testimonial-slide');
        const totalTestimonials = testimonials.length;

        function showTestimonial(index) {
            testimonials.removeClass('active');
            testimonials.eq(index).addClass('active');
            
            $('.testimonial-dot').removeClass('active');
            $('.testimonial-dot').eq(index).addClass('active');
        }

        function nextTestimonial() {
            currentTestimonial = (currentTestimonial + 1) % totalTestimonials;
            showTestimonial(currentTestimonial);
        }

        function prevTestimonial() {
            currentTestimonial = (currentTestimonial - 1 + totalTestimonials) % totalTestimonials;
            showTestimonial(currentTestimonial);
        }

        // Auto-advance testimonials
        if (testimonials.length > 1) {
            setInterval(nextTestimonial, 5000);
        }

        // Testimonial navigation
        $('.testimonial-next').on('click', nextTestimonial);
        $('.testimonial-prev').on('click', prevTestimonial);
        
        $('.testimonial-dot').on('click', function() {
            currentTestimonial = $(this).index();
            showTestimonial(currentTestimonial);
        });

        // Pool size calculator
        $('.size-calculator').on('submit', function(e) {
            e.preventDefault();
            
            var width = parseFloat($('#calc-width').val());
            var length = parseFloat($('#calc-length').val());
            var depth = parseFloat($('#calc-depth').val()) || 4;
            
            if (width && length) {
                var gallons = Math.round(width * length * depth * 7.48);
                var estimatedPrice = Math.round(gallons * 0.15); // Rough estimate
                
                $('.calculator-result').html(
                    '<h4>Pool Estimate</h4>' +
                    '<p><strong>Dimensions:</strong> ' + width + '\' × ' + length + '\' × ' + depth + '\'</p>' +
                    '<p><strong>Capacity:</strong> ' + gallons.toLocaleString() + ' gallons</p>' +
                    '<p><strong>Estimated Price:</strong> $' + estimatedPrice.toLocaleString() + '</p>' +
                    '<p><em>Contact us for an exact quote</em></p>'
                ).show();
            }
        });

        // FAQ accordion
        $('.faq-question').on('click', function() {
            var faqItem = $(this).parent();
            var answer = faqItem.find('.faq-answer');
            
            if (faqItem.hasClass('active')) {
                faqItem.removeClass('active');
                answer.slideUp();
            } else {
                $('.faq-item').removeClass('active');
                $('.faq-answer').slideUp();
                faqItem.addClass('active');
                answer.slideDown();
            }
        });

        // Newsletter signup
        $('.newsletter-form').on('submit', function(e) {
            e.preventDefault();
            
            var email = $(this).find('input[type="email"]').val();
            var form = $(this);
            
            if (email) {
                form.find('.submit-btn').prop('disabled', true).text('Subscribing...');
                
                // Simulate subscription (replace with actual AJAX call)
                setTimeout(function() {
                    form.find('.submit-btn').prop('disabled', false).text('Subscribe');
                    $('.newsletter-success').show();
                    form[0].reset();
                }, 1500);
            }
        });

        // Back to top button
        $(window).on('scroll', function() {
            if ($(this).scrollTop() > 500) {
                $('.back-to-top').fadeIn();
            } else {
                $('.back-to-top').fadeOut();
            }
        });

        $('.back-to-top').on('click', function(e) {
            e.preventDefault();
            $('html, body').animate({scrollTop: 0}, 1000);
        });

    });

})(jQuery);
